<!--
  患者详情页面
  简化版本：直接通过visitSn获取患者详情
-->
<template>
  <PatientDetailLayout>
    <!-- 左侧医疗记录菜单 -->
    <template #medical-menu>
      <MedicalRecordMenu
        ref="medicalRecordMenuRef"
        :initial-selected="selectedMenuItem"
        :patient="patient"
        @item-select="handleMenuItemSelect"
      />
    </template>

    <!-- 右侧患者详情内容 -->
    <div class="patient-detail-content">
      <!-- 患者详情 -->
      <div v-if="patient" class="patient-detail">
        <!-- 入院记录 -->
        <AdmissionRecord
          v-if="selectedMenuItem === 'admission-record'"
          :patient="patient"
          @patient-data-updated="handlePatientDataUpdated"
        />

        <!-- 首次病程记录 -->
        <FirstCourseRecord
          v-else-if="selectedMenuItem === 'first-progress'"
          :patient="patient"
          @patient-data-updated="handlePatientDataUpdated"
        />

        <!-- 会诊记录 -->
        <ConsultationRecord
          v-else-if="selectedMenuItem === 'consultation'"
          :patient="patient"
        />

        <!-- 术前讨论记录 -->
        <PreoperativeDiscussion
          v-else-if="selectedMenuItem === 'preop-discussion'"
          :patient="patient"
        />

        <!-- 术前小结 -->
        <PreoperativeSummary
          v-else-if="selectedMenuItem === 'preop-summary'"
          :patient="patient"
        />

        <!-- 有创诊疗操作记录 -->
        <InvasiveProcedure
          v-else-if="selectedMenuItem === 'invasive-procedure'"
          :patient="patient"
        />

        <!-- 手术记录 -->
        <SurgeryRecord
          v-else-if="selectedMenuItem === 'surgery-record'"
          :patient="patient"
        />

        <!-- 术后首次病程记录 -->
        <PostoperativeFirstCourseRecord
          v-else-if="selectedMenuItem === 'postop-first'"
          :patient="patient"
          @patient-data-updated="handlePatientDataUpdated"
        />

        <!-- 出院记录 -->
        <DischargeRecord
          v-else-if="selectedMenuItem === 'discharge-record'"
          :patient="patient"
          @patient-data-updated="handlePatientDataUpdated"
        />

        <!-- TNM分期记录 -->
        <TnmStagingRecord
          v-else-if="selectedMenuItem === 'tnm-staging'"
          :patient="patient"
        />

        <!-- 日常病程记录 -->
        <DailyProgressRecord
          v-else-if="selectedMenuItem === 'daily-progress'"
          :patient="patient"
          @count-updated="handleDailyProgressCountUpdate"
        />

        <!-- 诊断信息 -->
        <DiagnosisInfo
          v-else-if="selectedMenuItem === 'diagnosis-info'"
          :patient="patient"
        />

        <!-- 医嘱信息 -->
        <MedicalOrderInfo
          v-else-if="selectedMenuItem === 'medical-orders'"
          :patient="patient"
          @count-updated="handleCountUpdated"
        />

        <!-- 检验报告 -->
        <LabReport
          v-else-if="selectedMenuItem === 'lab-report'"
          :patient="patient"
        />

        <!-- 检查报告 -->
        <ExamReport
          v-else-if="selectedMenuItem === 'examination-report'"
          :patient="patient"
          @count-updated="handleCountUpdated"
        />

        <!-- 病理报告 -->
        <PathologyReport
          v-else-if="selectedMenuItem === 'pathology-report'"
          :patient="patient"
          @count-updated="handleCountUpdated"
        />

        <!-- 分子病理报告 -->
        <MolecularPathologyReport
          v-else-if="selectedMenuItem === 'molecular-pathology'"
          :patient="patient"
          @count-updated="handleCountUpdated"
        />

        <!-- 其他记录类型的占位符 -->
        <div v-else class="placeholder-content">
          <div class="placeholder-text">
            <h3>{{ currentMenuItemName }}</h3>
            <p>该功能正在开发中，敬请期待...</p>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-else-if="loading" class="loading-container">
        <el-loading
          element-loading-text="正在加载患者信息..."
          element-loading-spinner="el-icon-loading"
        />
      </div>

      <!-- 错误状态 -->
      <div v-else class="error-container">
        <div class="error-content">
          <el-icon class="error-icon"><Warning /></el-icon>
          <h3>患者信息加载失败</h3>
          <p>{{ errorMessage || '无法获取患者详情，请稍后重试' }}</p>
          <div class="error-actions">
            <el-button type="primary" @click="loadPatientData">重新加载</el-button>
            <el-button @click="goBack">返回列表</el-button>
          </div>
        </div>
      </div>
    </div>
  </PatientDetailLayout>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElLoading } from 'element-plus'
import { Warning } from '@element-plus/icons-vue'
import PatientDetailLayout from '@/layouts/PatientDetailLayout.vue'
import MedicalRecordMenu from '@/components/business/MedicalRecordMenu.vue'
import AdmissionRecord from '@/components/business/AdmissionRecord.vue'
import FirstCourseRecord from '@/components/business/FirstCourseRecord.vue'
import ConsultationRecord from '@/components/business/ConsultationRecord.vue'
import PreoperativeDiscussion from '@/components/business/PreoperativeDiscussion.vue'
import PreoperativeSummary from '@/components/business/PreoperativeSummary.vue'
import InvasiveProcedure from '@/components/business/InvasiveProcedure.vue'
import SurgeryRecord from '@/components/business/SurgeryRecord.vue'
import PostoperativeFirstCourseRecord from '@/components/business/PostoperativeFirstCourseRecord.vue'
import DischargeRecord from '@/components/business/DischargeRecord.vue'
import DailyProgressRecord from '@/components/business/DailyProgressRecord.vue'
import TnmStagingRecord from '@/components/business/TnmStagingRecord.vue'
import DiagnosisInfo from '@/components/business/DiagnosisInfo.vue'
import MedicalOrderInfo from '@/components/business/MedicalOrderInfo.vue'
import LabReport from '@/components/business/LabReport.vue'
import ExamReport from '@/components/business/ExamReport.vue'
import PathologyReport from '@/components/business/PathologyReport.vue'
import MolecularPathologyReport from '@/components/business/MolecularPathologyReport.vue'
import { apiServices } from '@/api'
// 第三方系统集成
import thirdPartyIntegration from '@/services/thirdPartyIntegration'
import { PatientDataAdapter } from '@/utils/patientDataAdapter'
import { getCurrentConfig } from '@/config/thirdPartyConfig'

// 路由相关
const route = useRoute()
const router = useRouter()

// 获取保存的菜单状态
const getSavedMenuState = () => {
  try {
    const saved = sessionStorage.getItem('selectedMedicalRecordMenu')
    if (saved) {
      const parsed = JSON.parse(saved)
      return {
        key: parsed.key || 'admission-record',
        name: parsed.name || '入院记录'
      }
    }
  } catch (error) {
  }
  return {
    key: 'admission-record',
    name: '入院记录'
  }
}

// 保存菜单状态
const saveMenuState = (menuData) => {
  try {
    sessionStorage.setItem('selectedMedicalRecordMenu', JSON.stringify(menuData))
  } catch (error) {
  }
}

// 获取初始菜单状态（优先使用URL参数，其次使用保存的状态）
const getInitialMenuState = () => {
  // 检查URL查询参数中是否有selectedMenuItem
  const urlSelectedMenuItem = route.query.selectedMenuItem

  // 只有在明确的功能跳转场景下才使用URL参数
  // 必须有fromMedicalOrder标记，或者是跳转到TNM分期记录
  const isFromMedicalOrderJump = route.query.fromMedicalOrder === 'true'
  const isJumpToTnmStaging = urlSelectedMenuItem === 'tnm-staging' && route.query.fromMedicalOrder === 'true'
  const isReturnToMedicalOrders = urlSelectedMenuItem === 'medical-orders' && route.query.fromMedicalOrder !== undefined

  const isValidJump = isFromMedicalOrderJump || isJumpToTnmStaging || isReturnToMedicalOrders

  if (urlSelectedMenuItem && isValidJump) {

    // 根据菜单项key获取对应的名称
    const menuItemNames = {
      'admission-record': '入院记录',
      'first-progress': '首次病程记录',
      'consultation': '会诊记录',
      'preop-discussion': '术前讨论记录',
      'preop-summary': '术前小结',
      'invasive-procedure': '有创诊疗操作记录',
      'surgery-record': '手术记录',
      'postop-first': '术后首次病程记录',
      'discharge-record': '出院记录',
      'tnm-staging': 'TNM分期记录',
      'daily-progress': '日常病程记录',
      'diagnosis-info': '诊断信息',
      'medical-orders': '医嘱信息',
      'lab-report': '检验报告',
      'examination-report': '检查报告',
      'pathology-report': '病理报告',
      'molecular-pathology': '分子病理报告'
    }

    return {
      key: urlSelectedMenuItem,
      name: menuItemNames[urlSelectedMenuItem] || urlSelectedMenuItem
    }
  }

  // 如果不是有效的功能跳转，则使用保存的状态
  return getSavedMenuState()
}

// 响应式数据
const patient = ref(null)
const loading = ref(false)
const errorMessage = ref('')
const initialMenuState = getInitialMenuState()
const selectedMenuItem = ref(initialMenuState.key)
const currentMenuItemName = ref(initialMenuState.name)

// 医疗记录菜单的引用
const medicalRecordMenuRef = ref(null)

// 第三方系统集成状态
const integrationStatus = ref({
  isInitialized: false,
  isInitializing: false,
  patientDataSent: false,
  error: null
})

// 防重复调用的Promise缓存
let loadPatientPromise = null

/**
 * 加载患者数据
 */
const loadPatientData = async () => {
  // 如果正在加载中，返回同一个Promise避免重复请求
  if (loadPatientPromise) {
    return await loadPatientPromise
  }

  // 创建新的加载Promise
  loadPatientPromise = (async () => {
    try {
      loading.value = true
      errorMessage.value = ''
      
      const visitSn = route.params.visitSn
      
      if (!visitSn) {
        throw new Error('缺少visitSn参数')
      }
      

      // 直接调用患者详情API
      const patientDetail = await apiServices.patient.getDetail(visitSn)
      
      if (!patientDetail) {
        throw new Error('API返回空数据')
      }
      
      // 处理患者数据
      const processedPatient = {
        ...patientDetail,
        formattedGenderAge: `${patientDetail.gender || '未知'} ${patientDetail.patientAge || patientDetail.age || '未知'}岁`
      }

      patient.value = processedPatient

      // 患者数据加载成功后，发送到第三方系统
      await sendPatientDataToThirdParty(processedPatient)
      
    } catch (error) {
      errorMessage.value = error.message || '网络请求失败，请检查网络连接'
      ElMessage.error('加载患者信息失败')
    } finally {
      loading.value = false
      // 清除Promise缓存，允许下次重新请求
      loadPatientPromise = null
    }
  })()

  return await loadPatientPromise
}

/**
 * 初始化第三方系统集成
 */
const initializeThirdPartyIntegration = async () => {
  console.log("患者数据1111111111111111111111",patient.value.visitDoctorNo)
  console.log("患者数据1111111111111111111111",patient.value.currentDeptCode)
  if (integrationStatus.value.isInitialized || integrationStatus.value.isInitializing) {
    return
  }

  try {
    integrationStatus.value.isInitializing = true
    integrationStatus.value.error = null


    // 获取配置
    const config = getCurrentConfig('yingchunhua')

    if (!config) {
      throw new Error('未找到第三方系统配置')
    }

    // 初始化第三方系统
    await thirdPartyIntegration.initialize({
      ...config,
      deptId: patient.value.currentDeptCode,
      doctorId: patient.value.visitDoctorNo
    })

    integrationStatus.value.isInitialized = true

  } catch (error) {
    integrationStatus.value.error = error.message
    // 不显示错误消息，避免影响用户体验
  } finally {
    integrationStatus.value.isInitializing = false
  }
}

/**
 * 获取第三方系统需要的患者历史数据
 */
const fetchPatientHistoryData = async (visitSn) => {
  try {

    // 使用统一的API服务获取患者历史数据
    const historyData = await apiServices.thirdPartyIntegration.getPatientHistory(visitSn)

    return historyData

  } catch (error) {
    throw error
  }
}

/**
 * 发送患者数据到第三方系统
 */
const sendPatientDataToThirdParty = async (patientData) => {
  if (!integrationStatus.value.isInitialized) {
    return
  }

  if (integrationStatus.value.patientDataSent) {
    return
  }

  try {

    // 获取visitSn
    const visitSn = patientData.visitSn || route.params.visitSn
    if (!visitSn) {
      return
    }

    // 获取符合第三方系统要求的患者历史数据
    const historyData = await fetchPatientHistoryData(visitSn)

    // 直接使用接口返回的数据格式，这已经符合第三方系统的要求
    // 数据格式：{ patientId, visitSn, dataPacket: [{ tableCode, data: [...] }] }

    // 发送数据到第三方系统
    await thirdPartyIntegration.sendPatientData(historyData)

    integrationStatus.value.patientDataSent = true

  } catch (error) {
    integrationStatus.value.error = error.message
    // 不显示错误消息，避免影响用户体验
  }
}

/**
 * 返回上一页
 */
const goBack = () => {
  router.go(-1)
}

/**
 * 处理菜单项选择
 */
const handleMenuItemSelect = (menuData) => {
  selectedMenuItem.value = menuData.key
  currentMenuItemName.value = menuData.name
  // 保存菜单状态到sessionStorage
  saveMenuState(menuData)
}

/**
 * 处理日常病程记录数量更新
 */
const handleDailyProgressCountUpdate = () => {
  // 通过ref调用医疗记录菜单的数量更新方法
  if (medicalRecordMenuRef.value && medicalRecordMenuRef.value.loadDailyProgressCount) {
    medicalRecordMenuRef.value.loadDailyProgressCount()
  }
}

/**
 * 处理数量更新（通用方法）
 */
const handleCountUpdated = () => {
  // 通过ref调用医疗记录菜单的数量更新方法
  if (medicalRecordMenuRef.value) {
    if (medicalRecordMenuRef.value.loadDailyProgressCount) {
      medicalRecordMenuRef.value.loadDailyProgressCount()
    }
    if (medicalRecordMenuRef.value.loadDiagnosisInfoCount) {
      medicalRecordMenuRef.value.loadDiagnosisInfoCount()
    }
    if (medicalRecordMenuRef.value.loadMedicalOrderCount) {
      medicalRecordMenuRef.value.loadMedicalOrderCount()
    }
    if (medicalRecordMenuRef.value.loadExamReportCount) {
      medicalRecordMenuRef.value.loadExamReportCount()
    }
    if (medicalRecordMenuRef.value.loadPathologyReportCount) {
      medicalRecordMenuRef.value.loadPathologyReportCount()
    }
    if (medicalRecordMenuRef.value.loadMolecularPathologyCount) {
      medicalRecordMenuRef.value.loadMolecularPathologyCount()
    }
    if (medicalRecordMenuRef.value.loadLabReportCount) {
      medicalRecordMenuRef.value.loadLabReportCount()
    }
  }
}

/**
 * 处理患者数据更新事件
 * 当医疗记录保存或删除后，重新加载患者数据以更新入院诊断等字段
 */
const handlePatientDataUpdated = async () => {
  try {
    await loadPatientData()
  } catch (error) {
    ElMessage.error('更新患者信息失败')
  }
}



// 监听路由查询参数变化
watch(() => route.query.selectedMenuItem, (newSelectedMenuItem) => {
  if (newSelectedMenuItem && newSelectedMenuItem !== selectedMenuItem.value) {

    // 更新选中的菜单项
    const menuItemNames = {
      'admission-record': '入院记录',
      'first-progress': '首次病程记录',
      'consultation': '会诊记录',
      'preop-discussion': '术前讨论记录',
      'preop-summary': '术前小结',
      'invasive-procedure': '有创诊疗操作记录',
      'surgery-record': '手术记录',
      'postop-first': '术后首次病程记录',
      'discharge-record': '出院记录',
      'tnm-staging': 'TNM分期记录',
      'daily-progress': '日常病程记录',
      'diagnosis-info': '诊断信息',
      'medical-orders': '医嘱信息',
      'lab-report': '检验报告',
      'examination-report': '检查报告',
      'pathology-report': '病理报告',
      'molecular-pathology': '分子病理报告'
    }

    selectedMenuItem.value = newSelectedMenuItem
    currentMenuItemName.value = menuItemNames[newSelectedMenuItem] || newSelectedMenuItem

    // 保存到sessionStorage
    saveMenuState({
      key: newSelectedMenuItem,
      name: currentMenuItemName.value
    })

  }
}, { immediate: false })

// 组件挂载时加载数据
onMounted(async () => {
  // 并行初始化第三方系统和加载患者数据
  await loadPatientData();
  await initializeThirdPartyIntegration();

  // 处理URL参数
  const hasSelectedMenuItem = route.query.selectedMenuItem
  const hasFromMedicalOrder = route.query.fromMedicalOrder !== undefined

  if (hasSelectedMenuItem || hasFromMedicalOrder) {

    // 保存当前菜单状态到sessionStorage
    saveMenuState({
      key: selectedMenuItem.value,
      name: currentMenuItemName.value
    })

    // 立即清理URL参数，避免刷新时一直保留
    const currentParams = { ...route.query }
    delete currentParams.selectedMenuItem
    delete currentParams.fromMedicalOrder

    // 如果还有其他参数，保留它们；如果没有，则清空query
    const hasOtherParams = Object.keys(currentParams).length > 0

    router.replace({
      name: route.name,
      params: route.params,
      query: hasOtherParams ? currentParams : {}
    }).catch(() => {
      // 忽略导航错误
    })
  }
})
</script>

<style scoped>
/* 患者详情内容区域 */
.patient-detail-content {
  width: 100%;
  height: 100%;
  /* 移除独立滚动，使用父容器的统一滚动 */
}

.patient-detail {
  border-radius: 8px;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.error-content {
  text-align: center;
  padding: 40px;
}

.error-icon {
  font-size: 48px;
  color: #f56c6c;
  margin-bottom: 16px;
}

.error-content h3 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 18px;
}

.error-content p {
  margin: 0 0 24px 0;
  color: #606266;
  font-size: 14px;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

/* 占位符内容样式 */
.placeholder-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.placeholder-text {
  text-align: center;
  padding: 40px;
}

.placeholder-text h3 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 18px;
}

.placeholder-text p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

/* 占位符内容样式 */
.placeholder-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.placeholder-text {
  text-align: center;
  padding: 40px;
}

.placeholder-text h3 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 18px;
}

.placeholder-text p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

/* 响应式设计由 PatientDetailLayout 处理 */
</style>
